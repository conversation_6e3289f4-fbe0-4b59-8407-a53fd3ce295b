from flask import Flask, render_template, request, jsonify
import pandas as pd
import pickle
import os
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score
from sklearn.preprocessing import LabelEncoder
from xgboost import XGBClassifier

app = Flask(__name__)

# Train model function
def train_model():
    # Load dataset
    data = pd.read_csv(r'C:\Users\<USER>\OneDrive\Desktop\pregnancy-care-prediction\pregnancy-care-prediction\pregnancy-care-prediction\data\B6.csv')
    X = data[['Age', 'SystolicBP', 'DiastolicBP', 'BS', 'BodyTemp', 'HeartRate']]
    y = data['RiskLevel']

    # Encode labels
    label_encoder = LabelEncoder()
    y_encoded = label_encoder.fit_transform(y)

    # Train/test split
    X_train, X_test, y_train, y_test = train_test_split(X, y_encoded, test_size=0.2, random_state=42)

    # Train model
    model = XGBClassifier(use_label_encoder=False, eval_metric='mlogloss', random_state=42)
    model.fit(X_train, y_train)

    # Accuracy reporting
    train_acc = accuracy_score(y_train, model.predict(X_train))
    test_acc = accuracy_score(y_test, model.predict(X_test))
    print(f"✅ Training Accuracy: {train_acc * 100:.2f}%")
    print(f"✅ Testing Accuracy : {test_acc * 100:.2f}%")

    # Save model and encoder
    os.makedirs('model', exist_ok=True)
    with open('model/model.pkl', 'wb') as f:
        pickle.dump(model, f)
    with open('model/label_encoder.pkl', 'wb') as f:
        pickle.dump(label_encoder, f)

    return model, label_encoder

# Load model and encoder or train new
try:
    with open('model/model.pkl', 'rb') as f:
        model = pickle.load(f)
    with open('model/label_encoder.pkl', 'rb') as f:
        label_encoder = pickle.load(f)
except:
    print("📦 Model files not found. Training new model...")
    model, label_encoder = train_model()

# Routes
@app.route('/')
def home():
    return render_template('index.html')

@app.route('/predict', methods=['POST'])
def predict():
    data = request.get_json()

    # Prepare input for prediction
    input_data = [[
        float(data['age']),
        float(data['systolic']),
        float(data['diastolic']),
        float(data['bs']),
        float(data['bodyTemp']),
        float(data['heartRate'])
    ]]

    # Predict numeric class and decode label
    pred_num = model.predict(input_data)[0]
    pred_label = label_encoder.inverse_transform([pred_num])[0]

    recommendations = get_recommendations(pred_label)

    return jsonify({
        'prediction': pred_label,
        'recommendations': recommendations
    })

def get_recommendations(risk_level):
    if risk_level == 'high risk':
        return [
            "Immediate medical consultation required",
            "Regular blood pressure monitoring",
            "Strict diet control",
            "Complete bed rest advised"
        ]
    elif risk_level == 'mid risk':
        return [
            "Schedule weekly check-ups",
            "Monitor blood pressure daily",
            "Moderate physical activity",
            "Balanced diet recommended"
        ]
    else:
        return [
            "Regular prenatal check-ups",
            "Maintain healthy lifestyle",
            "Stay hydrated",
            "Continue moderate exercise"
        ]

if __name__ == '__main__':
    app.run(debug=True)
