:root {
    --primary-color: #6c5ce7;
    --secondary-color: #a29bfe;
    --text-color: #2d3436;
    --light-gray: #dfe6e9;
    --white: #ffffff;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}



@media (max-width: 768px) {
    .logo h1 {
        font-size: 1.5rem; /* Reduce font size for smaller screens */
        text-align: center;
    }

    header {
        flex-direction: column;
        text-align: center;
    }
}

@media (max-width: 768px) {
    main {
        grid-template-columns: 1fr; /* Change to single-column layout */
        gap: 20px;
    }
}


@media (max-width: 768px) {
    .input-section, .results-section {
        padding: 15px;
    }

    input, button {
        font-size: 1rem; /* Increase font size for better touch accessibility */
    }
}

@media (max-width: 768px) {
    body {
        background-size: contain; /* Prevents image cropping */
        height: auto; /* Adjust height dynamically */
    }
}


button {
    font-size: 1rem;
    padding: 14px;
}

input {
    font-size: 1rem;
    padding: 12px;
}




body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: #f5f6fa;
}

.container {
    max-width: 100%;
    margin: 0 auto;
    padding: 20px;
}

header {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    margin-bottom: 40px;
}

.logo h1 {
    font-family: "Times New Roman", Times, serif;
    color: var(--white);
    font-size: 3rem;
}

.real-time {
    background-color: var(--primary-color);
    color: var(--white);
    padding: 10px 20px;
    border-radius: 20px;
}

main {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
}

.input-section, .results-section {
    background-color: var(--white);
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.form-group {
    margin-bottom: 20px;
}

label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

input {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--light-gray);
    border-radius: 5px;
    font-size: 1rem;
}

button {
    background-color: var(--primary-color);
    color: var(--white);
    padding: 12px 24px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    width: 100%;
    transition: background-color 0.3s;
}

button:hover {
    background-color: var(--secondary-color);
}

.risk-score {
    margin-bottom: 30px;
}

#riskLevel {
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary-color);
    margin: 10px 0;
}

.recommendations ul {
    list-style: none;
}

.recommendations li {
    margin-bottom: 10px;
    padding: 10px;
    background-color: var(--light-gray);
    border-radius: 5px;
}
