document.getElementById('predictionForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const formData = {
        age: document.getElementById('age').value,
        systolic: document.getElementById('systolic').value,
        diastolic: document.getElementById('diastolic').value,
        bs: document.getElementById('bs').value,
        bodyTemp: document.getElementById('bodyTemp').value,
        heartRate: document.getElementById('heartRate').value
    };

    try {
        const response = await fetch('/predict', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });

        const data = await response.json();
        
        // Display results
        document.querySelector('.results-section').style.display = 'block';
        document.getElementById('riskLevel').textContent = data.prediction;
        
        const recommendationsList = document.getElementById('recommendationsList');
        recommendationsList.innerHTML = '';
        data.recommendations.forEach(rec => {
            const li = document.createElement('li');
            li.textContent = rec;
            recommendationsList.appendChild(li);
        });
    } catch (error) {
        console.error('Error:', error);
        alert('An error occurred while making the prediction');
    }
});
