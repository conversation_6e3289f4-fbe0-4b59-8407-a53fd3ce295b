import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, confusion_matrix
import pickle
import os

def load_data():
    """Load and preprocess the dataset"""
    df = pd.read_csv("C:/Users/<USER>/OneDrive/Desktop/pregnancy-care-prediction/data/pregnancy_data.csv")

    
    # Extract features and target
    X = df[['Age', 'SystolicBP', 'DiastolicBP', 'BS', 'BodyTemp', 'HeartRate']]
    y = df['RiskLevel']
    
    return X, y

def train_model(X, y):
    """Train the Random Forest model"""
    # Split the data
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42
    )
    
    # Initialize and train the model
    model = RandomForestClassifier(
        n_estimators=100,
        max_depth=10,
        min_samples_split=5,
        min_samples_leaf=2,
        random_state=42
    )
    
    model.fit(X_train, y_train)
    
    # Evaluate the model
    train_score = model.score(X_train, y_train)
    test_score = model.score(X_test, y_test)
    
    print("\nModel Performance:")
    print(f"Training Accuracy: {train_score:.4f}")
    print(f"Testing Accuracy: {test_score:.4f}")
    
    # Generate detailed classification report
    y_pred = model.predict(X_test)
    print("\nClassification Report:")
    print(classification_report(y_test, y_pred))
    
    # Generate confusion matrix
    print("\nConfusion Matrix:")
    print(confusion_matrix(y_test, y_pred))
    
    return model, X_test, y_test

def save_model(model, output_path='model.pkl'):
    """Save the trained model"""
    with open(output_path, 'wb') as f:
        pickle.dump(model, f)
    print(f"\nModel saved successfully to {output_path}")

def main():
    # Create model directory if it doesn't exist
    if not os.path.exists('../model'):
        os.makedirs('../model')
    
    print("Loading data...")
    X, y = load_data()
    
    print("Training model...")
    model, X_test, y_test = train_model(X, y)
    
    # Save the model
    model_path = '../model/model.pkl'
    save_model(model, model_path)
    
    # Feature importance analysis
    feature_importance = pd.DataFrame({
        'feature': ['Age', 'SystolicBP', 'DiastolicBP', 'BS', 'BodyTemp', 'HeartRate'],
        'importance': model.feature_importances_
    })
    feature_importance = feature_importance.sort_values('importance', ascending=False)
    
    print("\nFeature Importance:")
    print(feature_importance)
    
    # Sample prediction
    print("\nSample Prediction:")
    sample_input = X_test.iloc[0].values.reshape(1, -1)
    sample_prediction = model.predict(sample_input)
    print(f"Input values: {sample_input[0]}")
    print(f"Predicted risk level: {sample_prediction[0]}")
    print(f"Actual risk level: {y_test.iloc[0]}")

if __name__ == "__main__":
    main()