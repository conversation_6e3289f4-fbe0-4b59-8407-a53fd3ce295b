<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Pregnancy Risk Prediction</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      background: #f2f2f2;
      padding: 40px;
    }
    .container {
      background: white;
      padding: 30px;
      max-width: 600px;
      margin: auto;
      box-shadow: 0 0 12px rgba(0, 0, 0, 0.1);
      border-radius: 8px;
    }
    h2 {
      text-align: center;
      color: #333;
    }
    label {
      display: block;
      margin-top: 15px;
      font-weight: bold;
    }
    input {
      width: 100%;
      padding: 10px;
      margin-top: 5px;
      border: 1px solid #ccc;
      border-radius: 5px;
    }
    button {
      margin-top: 20px;
      width: 100%;
      padding: 12px;
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 5px;
      font-size: 16px;
    }
    #result {
      margin-top: 25px;
      background: #e7f3e7;
      padding: 15px;
      border-left: 6px solid #4CAF50;
      display: none;
    }
  </style>
</head>
<body>
  <div class="container">
    <h2>Pregnancy Risk Predictor</h2>
    <form id="riskForm">
      <label>Age</label>
      <input type="number" name="age" required>

      <label>Systolic BP</label>
      <input type="number" name="systolic" required>

      <label>Diastolic BP</label>
      <input type="number" name="diastolic" required>

      <label>Blood Sugar (BS)</label>
      <input type="number" step="0.1" name="bs" required>

      <label>Body Temperature (°F)</label>
      <input type="number" step="0.1" name="bodyTemp" required>

      <label>Heart Rate</label>
      <input type="number" name="heartRate" required>

      <button type="submit">Predict Risk</button>
    </form>

    <div id="result">
      <h3>Prediction: <span id="prediction"></span></h3>
      <h4>Recommendations:</h4>
      <ul id="recommendations"></ul>
    </div>
  </div>

  <script>
    document.getElementById('riskForm').addEventListener('submit', function (e) {
      e.preventDefault();

      const form = e.target;
      const data = {
        age: form.age.value,
        systolic: form.systolic.value,
        diastolic: form.diastolic.value,
        bs: form.bs.value,
        bodyTemp: form.bodyTemp.value,
        heartRate: form.heartRate.value
      };

      fetch('/predict', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })
        .then(res => res.json())
        .then(res => {
          document.getElementById('prediction').innerText = res.prediction;
          const list = document.getElementById('recommendations');
          list.innerHTML = '';
          res.recommendations.forEach(rec => {
            const li = document.createElement('li');
            li.innerText = rec;
            list.appendChild(li);
          });
          document.getElementById('result').style.display = 'block';
        })
        .catch(err => {
          alert("Prediction failed. Please check your inputs or server.");
          console.error(err);
        });
    });
  </script>
</body>
</html>
